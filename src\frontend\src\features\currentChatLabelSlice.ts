import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CurrentChatLabelState } from "../interfaces";

const initialState: CurrentChatLabelState = {
  label: "",
};

const currentChatLabelSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setCurrentChatLabel: (state, action: PayloadAction<string>) => {
      state.label = action.payload;
    },
  },
});

export const { setCurrentChatLabel } = currentChatLabelSlice.actions;

export default currentChatLabelSlice.reducer;
