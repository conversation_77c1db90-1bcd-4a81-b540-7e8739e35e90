import { deleteData, getAllData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { clearMessages } from "../features/chatSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
import { clearRecentChat } from "../features/recentChatSlice";
import { AppDispatch } from "../store";
import { decryptionHandler } from "./decryptionHandler";

export const workspaceChatDelete = async (email: string, token: string, workspaceId: string, currentChatId: string, dispatch: AppDispatch) => {
    const allDbData =  await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users); 
    if(allDbData.length >0){
        const allDbId : string[] = [];
        for(const obj of allDbData){
            if(obj.workspaceId  === workspaceId){
                allDbId.push(obj.id)
            }
        }

        const sameChatWindowFlag = allDbId.includes(currentChatId);

        allDbId.forEach(async (id)=>{
            await deleteData(Stores.Users, id);
        });

        if(sameChatWindowFlag){
            dispatch(clearMessages());
            dispatch(setCurrentChatId(""));
            dispatch(setCurrentWorkspaceId(""));
        }
        dispatch(clearRecentChat());
        decryptionHandler(email, token, dispatch);

    }
}