import { useState } from "react";
import { deleteWorkspace } from "../services/workspacesService";
import { workspaceChatDelete } from "../utils/deleteChatAgainstWorkspace";
import { useAuth } from "./useAuth";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../store";
import { Workspace } from "../interfaces";

const useDeleteWorkspace = (setWorkspaces: React.Dispatch<React.SetStateAction<Workspace[]>>) => {
  const { acquireToken } = useAuth();
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deletingWorkspaceId, setDeletingWorkspaceId] = useState<string | null>(null);
  const dispatch = useDispatch();
  const currentChatId = useSelector((state: RootState) => state.currentChatId.id);
  const email = useSelector((state: RootState) => state.email.email);

  const handleDelete = async (id: string) => {
    try {
      setDeletingWorkspaceId(id);
      const getToken = await acquireToken();
      if (!getToken?.accessToken) {
        console.error("Failed to acquire token in handleChatButtonClick");
        return;
      }
      workspaceChatDelete(email, getToken?.accessToken, id, currentChatId, dispatch);

      if (!getToken) {
        throw new Error("Token acquisition failed.");
      }

      await deleteWorkspace(id, getToken?.accessToken);

      setWorkspaces(prevWorkspaces => prevWorkspaces.filter(workspace => workspace.id !== id));
    } catch (error) {
      console.error("Error deleting workspace:", error);
      setDeleteError("Failed to delete workspace.");
    } finally {
      setDeletingWorkspaceId(null);
    }
  };

  return { handleDelete, deleteError, deletingWorkspaceId };
};

export default useDeleteWorkspace;