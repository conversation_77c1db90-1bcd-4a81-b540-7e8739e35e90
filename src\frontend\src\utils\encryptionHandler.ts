import { addData, getAllData, deleteData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { AppDispatch } from "../store";
import { chatEncryption } from "./chatEncryption";
import { chatMessagesTransform } from "./chatMessagesTransform";
import { decryptionHandler } from "./decryptionHandler";
import { ChatMessageProps } from "./types";

export const encryptionHandler = async (chatMessages: ChatMessageProps[], email: string, token: string, dispatch: AppDispatch, currentChatId: string | undefined, currentWorkspaceId: string) => {
    try {
        const transformedMessages = await chatMessagesTransform(chatMessages);
        let workspaceId = currentWorkspaceId;
        const encryptedMessage = await chatEncryption(transformedMessages, email, token);
        let allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);
        if (allDbData.length > 0 && currentChatId) {
            const chatExists = allDbData.some(data => data.id === currentChatId);
            for(const elem of allDbData){
                if(elem.id === currentChatId){
                    workspaceId = elem.workspaceId
                }
            }
            if (chatExists) {
                await deleteData(Stores.Users, currentChatId);
                allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);
            }
        }
        if (encryptedMessage) {
            const encryptedData = transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];
            await addData(Stores.Users, newData);
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users); 
            decryptionHandler(email, token, dispatch);
            if(newDbData.length>0){
                dispatch(setCurrentChatId(newDbData[newDbData.length-1].id))
                dispatch(setApiCallFlag(false))
            }
        }
    } catch (error) {
        console.error('Error in encryptionHandler:', error);
    }
};

function transformEncryptedData(encryptedMessage: string, workspaceId:string) {
    const date = new Date();
    const isoString = date.toISOString();
    const uniqueId = makeAlphanumeric(isoString);
    return {
        hash: encryptedMessage,
        date: isoString,
        id: uniqueId,
        workspaceId: workspaceId
    };
}

export function makeAlphanumeric(dateString: string) {
    return dateString.replace(/[^a-zA-Z0-9]/g, '');
}
