import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { HeaderHamburgerSheetProps } from "../../interfaces";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../store";
import { markUpdatesAsViewed } from "../../features/versionSlice";
import { Dismiss16Filled } from "@fluentui/react-icons";

const HeaderHamburgerSheet: React.FC<HeaderHamburgerSheetProps> = ({
  isOpen,
  onClose,
  options,
  name,
  nameIcon: NameIcon,
  regionLabel,
  regionIcon: RegionIcon,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const isUpdateAvailable = useSelector((state: RootState) => state.version.isUpdateAvailable);

  const isActiveRoute = (href: string, parallelRoutes: string[]) => {
    return location.pathname === href || parallelRoutes.includes(location.pathname);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-800/75 flex justify-end z-50">
      <div className="bg-white dark:bg-zinc-800 w-64 h-full p-4">
        <button
          onClick={onClose}
          className="group border-2 rounded-full flex items-center justify-center p-2 bg-white hover:bg-gallagher-dark-300 hover:border-gallagher-dark-300 hover:shadow-lg"
        >
          <Dismiss16Filled className="text-black group-hover:text-white" />
        </button>
        <div className="mt-4">
          {options.map((option, index) => {
            const isActive = isActiveRoute(option.href, option.parallelRoutes || []);
            return (
              <button
                key={index}
                onClick={() => {
                  navigate(option.href);
                  onClose();
                  if (option.label === "Updates") {
                    dispatch(markUpdatesAsViewed());
                  }
                }}
                className={`w-full text-left px-4 py-2 flex items-center rounded ${
                  isActive
                    ? "bg-gallagher-blue-400 text-white"
                    : "text-gallagher-blue-600 dark:text-gallagher-blue-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                }`}
              >
                <option.icon
                  className={`mr-2 w-6 ${
                    isActive
                      ? "text-white"
                      : "text-gallagher-blue-600 dark:text-gallagher-blue-300"
                  }`}
                />
                <span className="ml-2">{option.label}</span>
                {option.label === "Updates" && isUpdateAvailable && (
                  <span className="mb-2 ml-1 h-2 w-2 bg-red-500 rounded-full animate-pulse-custom"></span>
                )}
              </button>
            );
          })}
          <div className="flex items-center mt-4 px-4 dark:text-white">
            <NameIcon className="mr-2 w-6 shrink-0 text-black dark:text-white" />
            <p className="truncate ml-2 grow">{name}</p>
          </div>
          <div className="flex items-center mt-4 px-4 dark:text-white">
            <RegionIcon className="mr-2 w-6 text-black dark:text-white" />
            <p className="ml-2">{regionLabel}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderHamburgerSheet;
