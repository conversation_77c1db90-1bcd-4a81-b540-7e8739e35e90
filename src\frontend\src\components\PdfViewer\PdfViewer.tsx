import { useState, useEffect, useRef } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { RootState } from "../../store";
import { useSelector, useDispatch } from "react-redux";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { PdfViewerProps } from "../../interfaces";
import {
  ZoomIn20Regular,
  ZoomOut20Regular,
  Print20Regular,
  Save20Regular,
  Dismiss20Regular,
} from "@fluentui/react-icons";
import { parsePdfUrl } from "../../utils/pdfUtils";
import { usePdfDocument } from "../../hooks/usePdfDocument";
import { closePanel } from "../../features/pdfViewerSlice";

// PDF worker URL
pdfjs.GlobalWorkerOptions.workerSrc = import.meta.env.VITE_PDF_WORKER;

const PdfViewer: React.FC<PdfViewerProps> = ({ pdfUrl }) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [scale, setScale] = useState(1.0);
  const currentWorkspaceId = useSelector(
    (state: RootState) => state.currentWorkspaceId.id
  );
  const containerRef = useRef<HTMLDivElement | null>(null);
  const dispatch = useDispatch();
  const { fileName, pageParam } = parsePdfUrl(pdfUrl);
  const initialPageNumber = pageParam
    ? Math.max(parseInt(pageParam.split("=")[1], 10), 1)
    : 1;

  const { pdfBlob, error } = usePdfDocument(
    currentWorkspaceId,
    fileName,
  );

  useEffect(() => {
    if (pdfBlob) {
      const pageElement = containerRef.current?.querySelector(
        `.react-pdf__Page[data-page-number="${initialPageNumber}"]`
      );
      if (pageElement) {
        pageElement.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, [pdfBlob, initialPageNumber]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);

    // Scroll to the initial page after the document is fully loaded
    setTimeout(() => {
      const pageElement = containerRef.current?.querySelector(
        `.react-pdf__Page[data-page-number="${initialPageNumber}"]`
      );
      if (pageElement) {
        pageElement.scrollIntoView({ behavior: "smooth" });
      }
    }, 100); // Adjust the time as needed
  };

  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.25, 1.5));
  };

  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.25, 0.5));
  };

  const handlePrint = async () => {
    if (pdfBlob) {
      const url = URL.createObjectURL(pdfBlob);
      const loadingTask = pdfjs.getDocument(url);
      const pdf = await loadingTask.promise;
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      document.body.appendChild(iframe);

      const printDocument = async () => {
        const pdfDoc = await pdf.getData();
        const blob = new Blob([pdfDoc], { type: "application/pdf" });
        const blobUrl = URL.createObjectURL(blob);
        iframe.src = blobUrl;
        iframe.onload = () => {
          if (iframe.contentWindow) {
            iframe.contentWindow.print();
          }
        };
      };

      printDocument();
    }
  };

  const handleSave = () => {
    if (pdfBlob) {
      const url = URL.createObjectURL(pdfBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const handleClosePanel = () => {
    dispatch(closePanel()); // Close the panel
  };

  return (
    <div
      ref={containerRef}
      className="h-screen overflow-hidden relative bg-gray-200 dark:bg-zinc-800"
    >
      {/* Floating toolbar */}
      <div className="fixed top-15 right-4 z-50 bg-gray-100 dark:bg-zinc-700 rounded-lg shadow-lg">
        <div className="flex flex-row">
          <button
            onClick={handleZoomIn}
            className="cursor-pointer text-xl text-gray-700 hover:text-gray-900 dark:text-white rounded-lg p-2 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500"
            title="Zoom In"
          >
            <ZoomIn20Regular />
          </button>
          <button
            onClick={handleZoomOut}
            className="cursor-pointer text-xl text-gray-700 hover:text-gray-900 dark:text-white rounded-lg p-2 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500"
            title="Zoom Out"
          >
            <ZoomOut20Regular />
          </button>
          <button
            onClick={handlePrint}
            className="cursor-pointer text-xl text-gray-700 hover:text-gray-900 dark:text-white rounded-lg p-2 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500"
            title="Print"
          >
            <Print20Regular />
          </button>
          <button
            onClick={handleSave}
            className="cursor-pointer text-xl text-gray-700 hover:text-gray-900 dark:text-white rounded-lg p-2 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500"
            title="Save"
          >
            <Save20Regular />
          </button>
          <button
            onClick={handleClosePanel}
            className="cursor-pointer text-xl text-gray-700 hover:text-gray-900 dark:text-white rounded-lg p-2 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500"
            title="Close"
          >
            <Dismiss20Regular />
          </button>
        </div>
      </div>
      
      <div className="h-full w-full overflow-auto">
        {error && <div className="text-red-500 p-4">{error}</div>}
        {pdfBlob && (
          <Document
            file={URL.createObjectURL(pdfBlob)}
            onLoadSuccess={onDocumentLoadSuccess}
          >
            {Array.from(new Array(numPages), (_, index) => (
              <div key={`page_${index + 1}`} className="p-2">
                <Page pageNumber={index + 1} scale={scale} />
              </div>
            ))}
          </Document>
        )}
      </div>
    </div>
  );
};

export default PdfViewer;