import React, { useState, useEffect } from "react";
import { Copy20Regular, ClipboardPaste20Regular, Document20Regular, TextT20Regular, DismissCircle24Filled } from "@fluentui/react-icons";
import { useAuth } from "../../hooks/useAuth";
import { translateText, fetchLanguages } from "../../services/translationService";
import { setSelectedLanguage } from "../../features/translationLanguageSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { useNavigate } from "react-router";
import { initLanguageSettingDB, getLanguageSetting, saveLanguageSetting } from "../../db/languageDB";
import { LANGUAGE_SETTINGS_ID } from "../../constants/dbConstants";
import Toast from "../Modal/ToastModal/Toast";
import useToast from "../../hooks/useToast";

const TextTranslationTab: React.FC = () => {
    const [text, setText] = useState("");
    const [translatedText, setTranslatedText] = useState("");
    const [justTranslated, setJustTranslated] = useState(false);
    const [languages, setLanguages] = useState<{ code: string, name: string }[]>([]);
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<"text" | "document">("text");
    const { acquireToken } = useAuth();
    const selectedLanguage = useSelector((state: RootState) => state.translationLanguageSelection.selectedLanguage); // Use Redux state
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { toasts, triggerToast, closeToast } = useToast();


    useEffect(() => {
        const loadLanguages = async () => {
            const getToken = await acquireToken();
            if (!getToken?.accessToken) {
                console.error("Token acquisition failed.");
                return;
            }

            const availableLanguages = await fetchLanguages(getToken.accessToken);
            setLanguages(availableLanguages);
        };

        loadLanguages();
    }, [acquireToken]);

    useEffect(() => {
        const loadLanguageFromDb = async () => {
            const initialized = await initLanguageSettingDB();
            if (!initialized) return;

            const getToken = await acquireToken();
            if (!getToken?.accessToken) {
                console.error("Token acquisition failed.");
                return;
            }

            const availableLanguages = await fetchLanguages(getToken.accessToken);
            setLanguages(availableLanguages);

            const stored = await getLanguageSetting(LANGUAGE_SETTINGS_ID);

            if (stored?.selectedLanguage && availableLanguages.some((lang) => lang.code === stored.selectedLanguage)
            ) {
                dispatch(setSelectedLanguage(stored.selectedLanguage));
            } else {
                dispatch(setSelectedLanguage(""));
            }
        };

        loadLanguageFromDb();
    }, [dispatch, acquireToken]);

    const handleTranslate = async () => {
        if (!selectedLanguage) {
            triggerToast({
                text: `Please select a language.`,
                icon: <DismissCircle24Filled />,
                duration: 7,
                position: "top-center",
                bgColor: "bg-red-500",
            });
            return;
        } if (!text) {
            triggerToast({
                text: `Please enter text to translate.`,
                icon: <DismissCircle24Filled />,
                duration: 7,
                position: "top-center",
                bgColor: "bg-red-500",
            });
            return;
        }

        setLoading(true);

        const token = await acquireToken();
        if (!token?.accessToken) throw new Error("Token acquisition failed.");
        try {
            const result = await translateText(text, selectedLanguage, token.accessToken);

            setTranslatedText(result);
            setJustTranslated(true);
            setTimeout(() => setJustTranslated(false), 1000);
        } catch (error) {
            console.error("Translation failed:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleClear = () => {
        setText("");
        setTranslatedText("");
    };

    const pasteText = async () => {
        try {
            const clipboardText = await navigator.clipboard.readText();
            setText(clipboardText)
        } catch (error) {
            console.error("Failed to copy:", error);
        }
    };

    const copyText = (text: string) => {
        if (!text) return;
        navigator.clipboard.writeText(text).then(() => {
        }).catch(err => console.error("Failed to copy:", err));
    };


    return (
        <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800">
            <div className="w-full max-w-4xl text-gallagher-blue-600 dark:text-gallagher-blue-200 flex items">
                <Document20Regular className="mr-1" />
                <button className={`tab ${activeTab === "document" ? "underline cursor-pointer" : "cursor-pointer"}`}
                    onClick={() => {
                        setActiveTab("document")
                        navigate("/doc-translation");
                    }}>
                    Document Translation
                </button>
                <span className="hidden lg:block text-zinc-300 dark:text-zinc-500 px-2">|</span>
                <TextT20Regular className="mr-1" />
                <button className={`tab ${activeTab === "text" ? "underline cursor-pointer" : "cursor-pointer"}`}
                    onClick={() => {
                        setActiveTab("text")
                        navigate("/text-translation");
                    }}>
                    Text Translation
                </button>
            </div>
            <h1 className="text-2xl font-bold p-4 dark:text-gray-300">
                Text Translation</h1>
            <div className="w-full max-w-4xl">
                <div className="flex justify-between w-full my-5">
                    <label className="text dark:text-gray-300">
                        Translate text into your selected language. A <a href="https://ajg0.sharepoint.com/:b:/r/teams/GO-ai_at_Gallagher/Shared%20Documents/Gallagher%20AI%20Text%20Translation%20Quick%20Start%20Guide.pdf?csf=1&web=1&e=izLwGI" target="_blank" className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"> Quick Start guide </a>
                        is available should you require assistance.
                    </label>
                </div>
                <div className="mb-4 w-full max-w-xs dark:text-black ">
                    <select
                        id="language"
                        value={selectedLanguage}
                        onChange={(e) => {
                            const lang = e.target.value;
                            dispatch(setSelectedLanguage(lang));
                            saveLanguageSetting({ id: LANGUAGE_SETTINGS_ID, selectedLanguage: lang });
                        }}
                        className="cursor-pointer w-full p-2 border-2 rounded-sm dark:text-gray-300 dark:bg-zinc-800 dark:border-zinc-600`"
                    ><option value="" disabled>Select Language</option>

                        {languages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                                {lang.name}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="relative">
                    <textarea
                        value={text}
                        onChange={(e) => setText(e.target.value)}
                        placeholder="Enter text to translate..."
                        className="mt-2 p-2 border-2 rounded w-full h-80 dark:text-gray-300 dark:bg-zinc-800 dark:border-zinc-600"
                    />
                    <button
                        onClick={pasteText}
                        className="cursor-pointer absolute right-2 bottom-2 bg-transparent p-1 rounded text-gray-600 hover:bg-gray-200 hover:text-black dark:text-gray-300 dark:hover:text-white dark:hover:bg-gallagher-dark-300"
                    >
                        <ClipboardPaste20Regular />
                        Paste
                    </button>
                </div >
                <div className="flex items-center justify-between mt-2">
                    <button onClick={handleClear} className="cursor-pointer bg-white px-4 py-2 hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white">
                        Clear
                    </button>
                    <button onClick={handleTranslate} disabled={loading} className="cursor-pointer ml-auto bg-white hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:text-white hover:shadow-md border-2 hover:border-gallagher-dark-300 p-2 px-4 rounded box-border dark:bg-zinc-700 dark:border-zinc-600 dark:text-gray-300 dark:hover:text-white">
                        {loading ? "Translating..." : "Translate"}
                    </button>
                </div>
                <div className="relative">
                    {translatedText && (
                        <p className={`mt-4 p-4 border-2 rounded bg-gallagher-blue-50 dark:text-gray-300 dark:bg-zinc-800 dark:border-zinc-600 whitespace-pre-wrap transition-all duration-500 ${justTranslated ? "border-zinc-500 shadow-zinc-300 shadow-md" : "border-zinc-300 dark:border-zinc-600"}`}
                        >
                            {translatedText}
                            <button
                                onClick={() => copyText(translatedText)}
                                className="cursor-pointer absolute right-2 bottom-[-36px] bg-transparent p-1 rounded text-gray-600 hover:bg-gray-200 hover:text-black dark:text-gray-300 dark:hover:text-white dark:hover:bg-gallagher-dark-300"
                            >
                                <Copy20Regular />
                                Copy
                            </button></p>)}
                            {toasts.map((toast) => (
                        <Toast
                            key={toast.id}
                            id={toast.id}
                            position={toast.position}
                            text={toast.text}
                            icon={toast.icon}
                            duration={toast.duration}
                            onClose={() => closeToast(toast.id)}
                            bgColor={toast.bgColor}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default TextTranslationTab;