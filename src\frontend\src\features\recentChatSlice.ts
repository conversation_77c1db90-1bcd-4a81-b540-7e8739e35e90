import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RecentChatState } from "../interfaces";

const initialState: RecentChatState[] = [];

const recentChatSlice = createSlice({
  name: "recentChat",
  initialState,
  reducers: {
    setRecentChat: (state, action: PayloadAction<{ id: string, text: string }>) => {
      const { id, text } = action.payload;
      const cleanedText = text.split('\n<')[0].trim();
      const existingIndex = state.findIndex((chat) => chat.id === id);

      if (existingIndex !== -1) {
        state[existingIndex].text = cleanedText;
      } else {
        state.push({ id, text: cleanedText });
      }    },
    clearRecentChat: () => {
      return [];
    }
  },
});

export const { setRecentChat, clearRecentChat } = recentChatSlice.actions;

export default recentChatSlice.reducer;
