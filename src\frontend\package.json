{"name": "chatfrontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "localhost": "vite --mode localhost", "build": "tsc -b && vite build --mode production", "build:dev": "tsc -b && vite build --mode development", "build:test": "tsc -b && vite build --mode test", "postbuild": "shx cp ./node_modules/pdfjs-dist/build/pdf.worker.min.mjs ./dist/assets/pdf.worker.min.mjs && shx mv ./dist/assets/pdf.worker.min.mjs ./dist/assets/pdf.worker.min.js && shx mkdir -p ./dist/assets/styles && shx cp ./node_modules/highlight.js/styles/atom-one-dark.min.css ./dist/assets/styles/ && shx cp ./node_modules/highlight.js/styles/atom-one-light.min.css ./dist/assets/styles/ && shx cp ./node_modules/github-markdown-css/github-markdown-dark.css ./dist/assets/styles/ && shx cp ./node_modules/github-markdown-css/github-markdown-light.css ./dist/assets/styles/", "test": "pnpm run build:test", "vitest": "vitest", "coverage": "vitest run --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --mode production", "preview:dev": "vite preview --mode development", "preview:test": "vite preview --mode test"}, "dependencies": {"@azure/msal-browser": "^4.13.0", "@azure/msal-react": "^3.0.12", "@fluentui/react-icons": "^2.0.302", "@microsoft/applicationinsights-react-js": "^17.3.6", "@microsoft/applicationinsights-web": "^3.3.8", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/postcss": "^4.1.8", "@types/react": "^19.1.6", "dompurify": "^3.2.6", "github-markdown-css": "^5.8.1", "he": "^1.2.0", "highlight.js": "^11.11.1", "history": "^5.3.0", "js-cookie": "^3.0.5", "marked": "^15.0.12", "pdfjs-dist": "4.8.69", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-redux": "^9.2.0", "react-router": "^7.6.1", "react-router-dom": "^7.6.1", "react-textarea-autosize": "^8.5.9", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.8"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.29", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.2.0", "@vitest/ui": "^3.2.0", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "fake-indexeddb": "^6.0.1", "jsdom": "^26.1.0", "shx": "^0.4.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.0"}, "packageManager": "pnpm@9.12.1+sha512.e5a7e52a4183a02d5931057f7a0dbff9d5e9ce3161e33fa68ae392125b79282a8a8a470a51dfc8a0ed86221442eb2fb57019b0990ed24fab519bf0e1bc5ccfc4"}