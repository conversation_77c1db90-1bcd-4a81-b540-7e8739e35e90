import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import AppRouter from "./router";
import { Provider } from "react-redux";
import store from "./store";
import "highlight.js/styles/atom-one-light.min.css";

import { Msal<PERSON>rovider } from "@azure/msal-react";
import { PublicClientApplication } from "@azure/msal-browser";
import { msalConfig } from "./authConfig";
import DisclaimerModal from "./components/Modal/DisclaimerModal/DisclaimerModal";
import { AppInsightsContext } from "@microsoft/applicationinsights-react-js";
import { reactPlugin } from "./services/appInsightService";
import useThemeSwitcher from "./hooks/useThemeSwitcher";

const pca = new PublicClientApplication(msalConfig);
await pca.initialize();

const App = () => {
  useThemeSwitcher(); // Dark mode hook

  return (
    <MsalProvider instance={pca}>
      <Provider store={store}>
        <AppInsightsContext.Provider value={reactPlugin}>
          <AppRouter />
          <DisclaimerModal />
        </AppInsightsContext.Provider>
      </Provider>
    </MsalProvider>
  );
};

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);