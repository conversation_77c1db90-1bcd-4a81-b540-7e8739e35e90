import { AccountInfo } from "@azure/msal-browser";
import { ReactNode } from "react";

export interface ChatInputProps {
    onMessageComplete: (index: number) => void;
  }

  export interface BackendMessage {
    role: string;
    content: ChatItemContent[];
  }
  
  export interface ChatItemContent {
    type: string;
    value: string | object;
  }
  
  export interface ChatItem {
    Role: string;
    Content: ChatItemContent[];
  }
  
  export interface Chat {
    workspace_id: string | null;
    settings: object;
    messages: ChatItem[];
  }

  export interface ChatMessageProps {
    user: string;
    text: string;
    chatLabel:string;
    isStreamComplete: boolean;
    messageId: number;
    workspaceId: string;
    documentId: string;
    onPdfClick?: (pdfUrl: string) => void;
    copyButtonRef?: (element: HTMLDivElement | null) => void;
  }

  export interface ChatState {
    messages: ChatMessageProps[];
  }

  export interface AllChatState{
    allChat:  Record<string, ChatMessageProps[]>;
  }

  export interface ApiCallFlagState{
    status: boolean;
  }

  export interface SessionFlagState{
    status: boolean;    
  }

  export interface TranslationFileUploadFlagState {
    status : boolean;
    newDocument: boolean; 
  }

  export interface TranslationLanguageState{
    selectedLanguage: string;
  }

  export interface LanguageSettingsType{
    id: string;
    selectedLanguage: string;
  }

  export interface CompleteMessageState {
    [key: number]: boolean;
  }
  
  export interface UserState {
    name: string;
  }

  export interface WrongFileUploadState {
    status: boolean
  }

  export interface EmailState {
    email:string;
  }

  export interface TokenState {
    token:string;
  }

  export interface CurrentChatIdState {
    id: string;
  }

  export interface CurrentChatLabelState {
    label: string;
  }

  export interface CurrentWorkspaceId {
    id: string;
  }

  export interface ChatHistoryOpenState {
    isOpen: boolean;
  }

  export type RecentChatState = {
    id: string;
    text: string;
  }

  // Define a type for Workspace
  export interface Workspace {
    id: string;
    name: string;
    description: string;
    documents: DocumentRecord[];
  }
  
  // Define a type for Workspace input (for creation and update)
  export interface WorkspaceInput {
    name: string;
    description: string;
  } 
  
  export interface DocumentRecord {
    name: string;
    processed: 0 | 1 | 2;
    expires?: string;
  }

  export interface PdfViewerProps {
    pdfUrl: string;
    onClosePanel: () => void;
  }

  export type RegionKey = 'US' | 'CN' | 'UK' | 'AU' | 'LOCALE';

export  interface HeaderHamburgerSheetProps {
    isOpen: boolean;
    onClose: () => void;
    options: { label: string; href: string, icon: React.ElementType, parallelRoutes:string[]|[]}[];
    name: string;
    nameIcon: React.ElementType;
    regionLabel: string;
    regionIcon: React.ElementType;
  }

export interface AdjustableWindowProps {
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
}

//indexedDB -setting
export interface UserSettingsTypes {
  id: string;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  template: string |undefined;
}

// useToast hook and Toast component
export interface ToastProps {
  position:
    | "top-left"
    | "top-center"
    | "top-right"
    | "mid-left"
    | "mid-center"
    | "mid-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
  id: number;
  className?: string;
  title?: string;
  text: string;
  icon?: ReactNode;
  duration?: number;
  onClose: () => void;
  bgColor?: string;
}

export interface ConfirmToastProps {
    position:
    | "top-left"
    | "top-center"
    | "top-right"
    | "mid-left"
    | "mid-center"
    | "mid-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
    text: string;
    icon?: React.ReactNode;
    onConfirm: () => void;
    onCancel: () => void;
    bgColor?: string;
}

export interface BottomButtonsProps {
  workspaceIdFromUrl: string | null;
  activeAccount: AccountInfo |null;
  save: () => void;
}

export interface InputSectionProps {
  workspaceIdFromUrl: string | null;
  name: string;
  description: string;
  inputChange: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export interface EditableChatTabLabelProps{
    text: string;
    onSave: (newText:string)=>void;
    onCancel: ()=> void;
}