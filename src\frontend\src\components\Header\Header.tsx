import { useSelector } from "react-redux";
import logoLight from "../../assets/GallagherAI.png";
import logoDark from "../../assets/GallagherAI-dark.png";
import { RootState } from "../../store";
import { useAuth } from "../../hooks/useAuth";
import React from "react";
import {
  Navigation24Regular,
  Dismiss24Regular,
  Person24Regular,
  GlobePerson24Regular,
  ChevronDown24Regular,
  ChevronUp24Regular,
} from "@fluentui/react-icons";
import { useHeader } from "../../hooks/useHeader";
import {
  MAX_USER_NAME_LENGTH,
  options,
  optionsForDropdown,
  optionsToShow,
} from "../../constants/HeaderConstants";
import HeaderHamburgerSheet from "./HeaderHamburgerSheet";
import { RegionKey } from "../../interfaces";
import { REGION_LABELS } from "../../constants/constants";
import { useNavigate } from "react-router-dom";
import ThemeToggle from "../ThemeToggle/ThemeToggle";
import useThemeSwitcher from "../../hooks/useThemeSwitcher";
import Tooltip from "../Tooltip/Tooltip";
import useVersionCheck from "../../hooks/useVersionCheck";

const truncateName = (name: string, maxLength: number) => {
  return name.length > maxLength ? `${name.slice(0, maxLength)}...` : name;
};

const Header: React.FC = () => {
  const name = useSelector((state: RootState) => state.user.name);
  const region = useSelector((state: RootState) => state.region.region); // Get the region from the Redux state
  const {} = useAuth();
  const {
    dropdownOpen,
    hamburgerOpen,
    toggleDropdown,
    toggleHamburger,
    dropdownRef,
    location,
  } = useHeader();
  const navigate = useNavigate();
  const { theme } = useThemeSwitcher();
  const { isUpdateAvailable, markUpdatesAsViewed } = useVersionCheck();

  const isActiveRoute = (href: string, parallelRoutes: string[]) => {
    return (
      location.pathname === href || parallelRoutes.includes(location.pathname)
    );
  };

  useThemeSwitcher();

  return (
    <header className="flex bg-white dark:bg-zinc-800 w-full h-[56px] border-b-2 border-b-blue-0 dark:border-b-zinc-700 fixed top-0 z-50">
      <div className="px-4 py-2 flex items-center justify-between w-full">
        <a onClick={() => {navigate("/")}} className="flex items-center text-blue-10 cursor-pointer">
          <img
            src={theme === "dark" ? logoDark : logoLight}
            alt="Logo"
            className="h-8 w-[251px] mr-2"
          />
        </a>
        <div className="flex items-center">
          <div className="hidden lg:flex items-center">
            {optionsToShow.map((option, index) => {
              const isActive = isActiveRoute(
                option.href,
                option.parallelRoutes || []
              );
              return (
                <React.Fragment key={index}>
                  <button
                    onClick={() => {
                      navigate(option.href);
                      if (option.label === "Updates") {
                        markUpdatesAsViewed();
                      }
                    }}
                    className={`text-base p-2 flex items-center rounded cursor-pointer ${
                      isActive
                        ? "text-gallagher-blue-400 dark:text-gallagher-blue-100 underline underline-offset-4"
                        : "text-gallagher-blue-600 dark:text-gallagher-blue-300 hover:text-gallagher-blue-500 dark:hover:text-gallagher-blue-200"
                    }`}
                  >
                    <option.icon className="mr-2" />
                    {option.label}
                    {option.label === "Updates" && isUpdateAvailable && (
                      <span className="mb-2 ml-1 h-2 w-2 bg-red-500 rounded-full animate-pulse-custom"></span>
                    )}
                  </button>
                  {index < optionsToShow.length - 1 && (
                    <span className="text-zinc-300 dark:text-zinc-500">|</span>
                  )}
                </React.Fragment>
              );
            })}
          </div>
          <span className="hidden lg:block text-zinc-300 dark:text-zinc-500">|</span>
          <div className="relative hidden lg:block" ref={dropdownRef}>
            <button
              onClick={toggleDropdown}
              className="mx-2 text-gallagher-blue-600 hover:text-gallagher-blue-500 dark:text-gallagher-blue-300 dark:hover:text-gallagher-blue-200 flex items-center text-base cursor-pointer"
            >
              More
              {dropdownOpen ? (
                <ChevronUp24Regular className="ml-1" />
              ) : (
                <ChevronDown24Regular className="ml-1" />
              )}
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-zinc-800 border rounded-sm shadow-lg z-10">
                {optionsForDropdown.map((option, index) => {
                  const isActive = isActiveRoute(
                    option.href,
                    option.parallelRoutes || []
                  );
                  return (
                    <button
                      key={index}
                      onClick={() => {
                        navigate(option.href);
                        toggleDropdown();
                      }}
                      className={`cursor-pointer w-full z-50 text-left px-4 py-2 flex items-center rounded ${
                        isActive
                          ? "bg-gallagher-blue-400 text-white"
                          : "text-gallagher-blue-600 dark:text-gallagher-blue-300 hover:bg-gray-100 dark:hover:bg-zinc-700"
                      }`}
                    >
                      <option.icon
                        className={`mr-2 ${
                          isActive
                            ? "text-white"
                            : "text-gallagher-blue-600 dark:text-gallagher-blue-300"
                        }`}
                      />
                      {option.label}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
          <span className="hidden lg:block text-zinc-300 dark:text-zinc-500">|</span>
          <Tooltip message="Change Theme" position="bottom">
            <ThemeToggle />
          </Tooltip>
          <span className="hidden lg:block text-zinc-300 dark:text-zinc-500">|</span>
          <div className="hidden lg:flex items-center dark:text-gray-300">
            <Person24Regular className="mx-1" />
            <span className="truncate">
              {truncateName(name, MAX_USER_NAME_LENGTH)}
            </span>
          </div>
          <span className="hidden mx-2 lg:block text-zinc-300 dark:text-zinc-500">|</span>
          <div className="hidden lg:flex items-center dark:text-gray-300">
            <GlobePerson24Regular className="mr-1" />
            <span className="">
              {REGION_LABELS[region as RegionKey] || region || "Region"}
            </span>
          </div>
          <button
            onClick={toggleHamburger}
            className="lg:hidden ml-4 dark:text-gray-300"
            title="Menu"
          >
            {hamburgerOpen ? <Dismiss24Regular /> : <Navigation24Regular />}
          </button>
        </div>
      </div>
      <HeaderHamburgerSheet
        isOpen={hamburgerOpen}
        onClose={toggleHamburger}
        options={options}
        name={name}
        nameIcon={Person24Regular}
        regionLabel={REGION_LABELS[region as RegionKey] || region || "Region"}
        regionIcon={GlobePerson24Regular}
      />
    </header>
  );
};

export default React.memo(Header);
