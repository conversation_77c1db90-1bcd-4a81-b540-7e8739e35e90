import React, { useEffect, useState, useCallback } from "react";
import {
  Chat24Regular,
  Delete24Regular,
  SpinnerIos20Regular,
  Edit24Regular
} from "@fluentui/react-icons";
import { useNavigate } from "react-router";
import { RootState } from "../../../store";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import { getChatHistory } from "../../../utils/getChatHistory";
import { setCurrentChatId } from "../../../features/currentChatIdSlice";
import { clearMessages } from "../../../features/chatSlice";
import { clearFollowUpQuestions } from "../../../features/followUpQuestionsSlice";
import { deleteData, getAllData, updateHash } from "../../../db/chatDB";
import {Stores} from "../../../constants/dbConstants"
import { decryptionHandler } from "../../../utils/decryptionHandler";
import { clearRecentChat, setRecentChat } from "../../../features/recentChatSlice";
import { clearCompleteMessage } from "../../../features/completeMessageSlice";
import { closePanel } from "../../../features/pdfViewerSlice";
import { useAuth } from "../../../hooks/useAuth";
import Styles from "./ChatTab.module.css";
import { AdjustableWindow } from "../../../hooks/useAdjustableWindow";
import { setCurrentChatLabel } from "../../../features/currentChatLabelSlice";
import { MAX_RESOLUTION_FOR_MOBILE } from "../../../constants/HeaderConstants";
import { setChatHistoryOpen } from "../../../features/chatHistoryOpenSlice";
import { setCurrentWorkspaceId } from "../../../features/currentWorkspaceIdSlice";
import EditableChatTabLabel from "../../Chat/EditableChatTabLabel";
import { chatDecryption } from "../../../utils/chatDecryption";
import { chatEncryption } from "../../../utils/chatEncryption";
import { updateChatLabel } from "../../../features/AllChatHistorySlice";

const ChatTab: React.FC = React.memo(() => {
  const navigate = useNavigate();
  // Use typed selector hook for better type safety
  const chatTabs = useAppSelector((state: RootState) => state.recentChat);
  const email = useAppSelector((state: RootState) => state.email.email);
  const token = useAppSelector((state: RootState) => state.token.token);
  const { acquireToken } = useAuth();
  // Use typed selector hook for better type safety
  const apiCallFlag = useAppSelector(
    (state: RootState) => state.apiCallFlag.status
  );
  // Use typed selector hook for better type safety
  // const allChatHistory = useAppSelector(
  //     (state: RootState) => state.allChat.allChat
  //   );
  const [isLoading, setIsLoading] = useState(true);
  const [editableChatId, setEditableChatId] = useState<string | null>(null);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);
  const { width: sidebarWidth, handleResizeStart } = AdjustableWindow({});
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE);

  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const fetch = async () => {
      const allDbData = await getAllData(Stores.Users);

      // If we have chat tabs, we're not loading
      if (chatTabs.length > 0) {
        setIsLoading(false);
        return;
      }

      // If no database data exists, we're not loading (empty state)
      if (!allDbData || allDbData.length === 0) {
        setIsLoading(false);
        return;
      }

      // If we have database data but no chat tabs yet, we might be loading
      // But only set loading if we haven't already processed the data
      if (allDbData.length > 0 && chatTabs.length === 0) {
        // Check if decryption is in progress by looking at API call flag
        if (!apiCallFlag) {
          setIsLoading(true);

          // Set a timeout to prevent infinite loading (max 10 seconds)
          const timeout = setTimeout(() => {
            setIsLoading(false);
          }, 10000);

          setLoadingTimeout(timeout);
        }
      }
    };
    fetch();
  }, [chatTabs, apiCallFlag, setLoadingTimeout]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [loadingTimeout]);

  // Use typed selector hook for better type safety
  const currentChatId = useAppSelector(
    (state: RootState) => state.currentChatId.id
  );

  useEffect(() => {
    const selectedChat = chatTabs.find((chat) => chat.id === currentChatId);
    if (selectedChat) {
      dispatch(setCurrentChatLabel(selectedChat.text));
    } else {
      dispatch(setCurrentChatLabel(""));
    }
  }, [chatTabs, currentChatId, dispatch]);

  const handleChatButtonClick = useCallback(
    async (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      setEditableChatId(null);
      const chatId = (event.target as HTMLButtonElement).id;
      const getToken = await acquireToken();
      if (!getToken?.accessToken) {
        console.error("Failed to acquire token in handleChatButtonClick");
        return;
      }
      setIsLoading(true);
      dispatch(clearFollowUpQuestions());
      await getChatHistory(chatId, email, getToken?.accessToken, dispatch);
      dispatch(setCurrentChatId(chatId));
      dispatch(closePanel());
      setIsLoading(false);
      if (window.innerWidth < MAX_RESOLUTION_FOR_MOBILE) {
        dispatch(setChatHistoryOpen(false));
      }
    },
    [acquireToken, email, dispatch]
  );

  const handleNewChat = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.preventDefault();
      setEditableChatId(null);
      navigate("/");
      dispatch(setCurrentChatId(""));
      dispatch(clearMessages());
      dispatch(clearFollowUpQuestions());
      dispatch(setCurrentWorkspaceId(""));
      dispatch(closePanel());
      if (window.innerWidth < MAX_RESOLUTION_FOR_MOBILE) {
        dispatch(setChatHistoryOpen(false));
      }
    },
    [navigate, dispatch]
  );

  const handleDeleteChat = useCallback(
    async (event: React.MouseEvent<HTMLButtonElement>) => {
      event.preventDefault();
      const chatId = (event.currentTarget as HTMLButtonElement).id;
      const getToken = await acquireToken();

      if (chatId === currentChatId) {
        dispatch(clearMessages());
        dispatch(clearCompleteMessage());
        dispatch(setCurrentWorkspaceId(""));
      }
      await deleteData(Stores.Users, chatId);
      if (!getToken?.accessToken) {
        console.error("Failed to acquire token in handleDeleteChat");
        return;
      }
      await decryptionHandler(email, getToken?.accessToken, dispatch);
      const allDbData = await getAllData(Stores.Users);
      if (allDbData.length === 0) {
        dispatch(clearRecentChat());
        dispatch(setCurrentChatId(""));
      }
    },
    [acquireToken, email, currentChatId, dispatch]
  );

  const handleRelabel = async (chatId: string, newText: string) => {
    if (!chatId || !newText) return;
    setEditableChatId(null);

    const allChats = await getAllData<any>(Stores.Users);
    const chatToUpdate = allChats.find((chat) => chat.id === chatId);
    if (!chatToUpdate) return;
    try {
      const decryptedChat = await chatDecryption(
        chatToUpdate.hash,
        email,
        token
      );
      if (!decryptedChat) {
        console.error("failed to decrypt chat");
        return;
      }
      if (decryptedChat.length > 0 && decryptedChat[0].role === "user") {
        decryptedChat[0].chatLabel = newText;
      }
      const updatedHash = await chatEncryption(decryptedChat, email, token);
      if (!updatedHash) {
        console.error("failed to re-encryt chat");
        return;
      }
      await updateHash(Stores.Users, chatToUpdate.id, updatedHash);

      dispatch(updateChatLabel({ key: chatId, newLabel: newText }));
      dispatch(setRecentChat({ id: chatId, text: newText }));
    } catch (err) {
      console.error("Relabel failed:", err);
    }
  };

  return (
    <div className="h-full flex flex-col lg:flex-row bg-white dark:bg-zinc-800 dark:text-white">
      <div
        className={`flex relative flex-col h-full p-4 space-y-4 overflow-y-auto ${
          sidebarWidth ? "lg:w-auto" : "w-full"
        }`}
        style={
          window.innerWidth >= MAX_RESOLUTION_FOR_MOBILE
            ? { width: sidebarWidth }
            : {}
        }
      >
        <div
          className={`lg:p-4 lg:mt-[56px] lg:overflow-y-auto lg:fixed lg:top-0 lg:left-0 lg:right-0 ${
            !isLoading ? "lg:max-h-[calc(100vh-4rem)]" : "max-h-none"
          }`}
          style={isLargeScreen ? { maxWidth: sidebarWidth } : {}}
        >
          <button
            className={`group w-full flex items-center p-2 rounded dark:bg-zinc-700 ${
              apiCallFlag
                ? "bg-gallagher-blue-50 dark:bg-gallagher-blue-700 cursor-not-allowed"
                : "bg-gallagher-blue-50 hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500 cursor-pointer"
            } flex`}
            onClick={handleNewChat}
            disabled={apiCallFlag}
            aria-label="Start New Chat"
            title="Start a New Chat"
          >
            <Chat24Regular
              className={`mr-2 dark:text-white ${
                apiCallFlag
                  ? "text-gallagher-dark-100"
                  : "text-gallagher-dark-100 group-hover:text-gallagher-dark-200 dark:group-hover:text-white"
              }`}
            />
            New Chat
          </button>
          {isLoading && (
            <div className="flex justify-center items-center h-screen">
              <SpinnerIos20Regular
                className={`${Styles.spinner} h-8 w-8 text-gallagher-blue-50`}
              />
            </div>
          )}
          {chatTabs.length > 0 && (
            <h2 className="text-lg font-semibold block mt-4 mb-2">Recent chats</h2>
          )}
          <div className="flex flex-col space-y-2">
            {chatTabs.length > 0 &&
              chatTabs.map((chat) => (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between p-2 rounded cursor-pointer
                    ${
                      chat.id === currentChatId
                        ? "dark:bg-gallagher-blue-500 bg-gallagher-blue-200"
                        : "dark:bg-zinc-700 bg-gallagher-blue-50"
                    }
                    hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-400`}
                >
                  <Chat24Regular
                    className={`text-xl text-gallagher-dark-100 group-hover:text-gallagher-dark-200 dark:group-hover:text-gray-300 dark:text-white ${
                      chat.id === currentChatId ? "text-gallagher-dark-200" : ""
                    }`}
                  />
                  {editableChatId === chat.id ? (<EditableChatTabLabel text={chat.text} 
                  onSave={(newText)=>handleRelabel(chat.id,newText)}
                  onCancel={()=>setEditableChatId(null)}/>):
                  (<button
                    id={chat.id}
                    className="flex-1 ml-2 pr-2 text-base sm:text-sm truncate text-left break-words cursor-pointer"
                    onClick={handleChatButtonClick}
                    aria-label="Select Conversation"
                    title="Select Conversation"
                  >
                    {chat.text}
                  </button>)}
                  <div className="flex items-center gap-2">
                { editableChatId !== chat.id && <button 
                    id={chat.id}
                    className="text-xl text-gallagher-dark-100 hover:text-gallagher-dark-200 dark:text-white dark:hover:text-gray-300 cursor-pointer"
                    title="Edit chat name"
                    onClick={()=>setEditableChatId(chat.id)}
                  ><Edit24Regular/>
                  </button>}
                  <button
                    id={chat.id}
                    className="text-xl text-gallagher-dark-100 hover:text-gallagher-dark-200 dark:text-white dark:hover:text-gray-300 cursor-pointer"
                    onClick={handleDeleteChat}
                    aria-label="Delete Chat"
                    title="Delete chat"
                  > <Delete24Regular />
                  </button>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
      <div
        onMouseDown={handleResizeStart}
        className="w-[4px] cursor-col-resize bg-gray-300 dark:bg-zinc-700 hidden lg:block"
      />

      <div className="grow bg-white"></div>
    </div>
  );
});

export default ChatTab;