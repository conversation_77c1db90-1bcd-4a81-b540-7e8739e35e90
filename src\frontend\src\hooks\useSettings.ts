import { useState, useEffect } from 'react';
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, SYSTEM_TEMPLATE, CUSTOM_TEMPLATE_LIMIT } from "../constants/SettingsTabConstants";
import { Stores, USER_SETTINGS_ID } from "../constants/dbConstants";
import { initUserSettingDB, getUserSetting, saveUserSetting, getCustomTemplate, updateOrSaveCustomTemplate, getSelectedCustomTemplate, saveSelectedCustomTemplate } from "../db/settingDB";
import { UserSettingsTypes } from '../interfaces';
import { useDispatch, useSelector } from "react-redux";
import { setSetting } from '../features/settingsSlice';
import { CustomTemplatePropType, MergedTemplateListPropTypes } from '../types/dbTypes';
import { removeDuplicates } from '../utils/removeDuplicateTemplates';
import { setSessionCallFlag } from '../features/sessionFlagSlice';
import { RootState } from '../store';

const useSettings = () => {
  const [settings, setSettings] = useState<UserSettingsTypes>({
    id: USER_SETTINGS_ID,
    temperature: TEMPERATURE.minValue,
    topP: TOP_P.minValue,
    frequencyPenalty: FREQUENCY_PENALTY.minValue,
    presencePenalty: PRESENCE_PENALTY.minValue,
    template: SYSTEM_TEMPLATE.find((template) => template.default === true)?.description || ""
  });
  const [selectedTemplateLabel, setSelectedTemplateLabel] = useState<string>("");
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
  const [isTextareaDisabled, setIsTextareaDisabled] = useState<boolean>(true);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);
  const [textareaValue, setTextareaValue] = useState<string>(SYSTEM_TEMPLATE[0].description);
  const [templateList, setTemplateList] = useState<MergedTemplateListPropTypes[]>(SYSTEM_TEMPLATE);
  const [customTemplateDB, setCustomTemplateDB] = useState<CustomTemplatePropType[]>([]);
  const newSessionFlag = useSelector((state: RootState) => state.sessionFlag.status);
  const dispatch = useDispatch();
  const fetchTemplates = async () => {
    await initUserSettingDB();
    const customTemplate = await getCustomTemplate();
    setCustomTemplateDB(customTemplate);
    const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
    setTemplateList(uniqueTemplates);
  };
  useEffect(()=>{
      if(newSessionFlag){
        async function init() {
          await initUserSettingDB();
          await saveSelectedCustomTemplate(SYSTEM_TEMPLATE[0]);
          const defaultSettings = {
            id: USER_SETTINGS_ID,
            temperature: TEMPERATURE.defaultValue,
            topP: TOP_P.defaultValue,
            frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
            presencePenalty: PRESENCE_PENALTY.defaultValue,
            template: "",
          };
          const settings = await getUserSetting(USER_SETTINGS_ID);
          if (!settings) {
            saveUserSetting(defaultSettings);
            dispatch(setSetting(defaultSettings));
          }
          else {
            settings.template ="";
            saveUserSetting(settings);
          }
          dispatch(setSessionCallFlag(false));
          dispatch(setSetting(settings));
        }
        init();
      }
    },[])

  useEffect(() => {
    fetchTemplates();
  }, []);

  useEffect(() => {
    const customTemplates = templateList.filter((template) => template.template === "custom");
    setIsButtonDisabled(customTemplates.length >= CUSTOM_TEMPLATE_LIMIT);
  }, [templateList]);

  useEffect(() => {
    const fetchData = async () => {
      await initUserSettingDB();
      const savedSettings = await getUserSetting(Stores.UserSettings);
      const customTemplate = await getCustomTemplate();
      const selectedTemplate = await getSelectedCustomTemplate();

      if (savedSettings) {
        setSettings(savedSettings);
        dispatch(setSetting(savedSettings));
        if (selectedTemplate) {
          setSelectedTemplateLabel(selectedTemplate.label);
          setSelectedTemplateId(selectedTemplate.id);
          setTextareaValue(selectedTemplate.description);
          setIsTextareaDisabled(selectedTemplate.template === "system");
        } else {
          const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.description === savedSettings.template);
          if (defaultTemplate) {
            setSelectedTemplateLabel(defaultTemplate.label);
            setSelectedTemplateId(defaultTemplate.id);
            setIsTextareaDisabled(defaultTemplate.template === "system");
            setTextareaValue(defaultTemplate.description);
          }
        }
      } else {
        const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
        if (defaultTemplate) {
          setSelectedTemplateLabel(defaultTemplate.label);
          setSelectedTemplateId(defaultTemplate.id);
          setIsTextareaDisabled(defaultTemplate.template === "system");
          setTextareaValue(defaultTemplate.description);
        }
      }
      const uniqueTemplates = removeDuplicates([...SYSTEM_TEMPLATE, ...customTemplate]);
      setTemplateList(uniqueTemplates);
    };
    fetchData();
  }, [dispatch]);

  useEffect(() => {
    setTemplateList(prevTemplateList => {
      const updatedList = removeDuplicates([...prevTemplateList, ...customTemplateDB]);
      return updatedList;
    });
    const isNewSelectedTemplate = templateList.some((template) => template.id === selectedTemplateId);
    if (!isNewSelectedTemplate) {
      const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
      if (defaultTemplate) {
        setSelectedTemplateLabel(defaultTemplate.label);
        setSelectedTemplateId(defaultTemplate.id);
        setTextareaValue(defaultTemplate.description);
        setIsTextareaDisabled(defaultTemplate.template === "system");
      }
    }
  }, [customTemplateDB]);

  const handleTemplateChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedLabel = event.target.value;
    const selectedTemplate = templateList.find((template) => template.label === selectedLabel);
    if (selectedTemplate) {
      setTextareaValue(selectedTemplate.description);
      setSelectedTemplateLabel(selectedLabel);
      setSelectedTemplateId(selectedTemplate.id);
      setIsTextareaDisabled(selectedTemplate.template === "system");
    }
  };

  const handleTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextareaValue(event.target.value);
  };

  const saveSettings = async (newSettings: UserSettingsTypes) => {
    const updatedSettings = { ...newSettings, template: textareaValue===SYSTEM_TEMPLATE[0].description ? "" : textareaValue };
    await saveUserSetting(updatedSettings);
    await updateOrSaveCustomTemplate(selectedTemplateId, textareaValue, templateList);
    setSettings(updatedSettings);
    dispatch(setSetting(updatedSettings));
  };

  const resetSettings = async () => {
    const defaultSettings = {
      id: USER_SETTINGS_ID,
      temperature: TEMPERATURE.defaultValue,
      topP: TOP_P.defaultValue,
      frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
      presencePenalty: PRESENCE_PENALTY.defaultValue,
      template: "",
    };
    await saveUserSetting(defaultSettings);
    await saveSelectedCustomTemplate(SYSTEM_TEMPLATE.filter((template) => template.default === true)[0]);
    setSettings(defaultSettings);
    dispatch(setSetting(defaultSettings));
    const defaultTemplate = SYSTEM_TEMPLATE.find((template) => template.default === true);
    if (defaultTemplate) {
      setSelectedTemplateLabel(defaultTemplate.label);
      setSelectedTemplateId(defaultTemplate.id);
      setIsTextareaDisabled(defaultTemplate.template === "system");
      setTextareaValue(defaultTemplate.description);
    }
    console.log('Reset Settings:', defaultSettings); // Log reset settings
  };


  return {
    settings,
    selectedTemplateLabel,
    isTextareaDisabled,
    isButtonDisabled,
    textareaValue,
    setSettings, 
    handleTemplateChange,
    handleTextareaChange,
    saveSettings,
    resetSettings,
    templateList, 
  };
};

export default useSettings;