import { deleteData, getAllData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setCompleteMessage } from "../features/completeMessageSlice";
import { clearRecentChat } from "../features/recentChatSlice";
import { CompleteMessageState } from "../interfaces";
import { fetchWorkspaces } from "../services/workspacesService";
import { AppDispatch } from "../store";
import { chatDecryption } from "./chatDecryption";
import { apiResponseMessagesTransform } from "./chatMessagesTransform";
import { recentChatTabsData } from "./recentChatTabsData";
import {
  EncryptedDataProps,
  ChatMessageProps,
  TransformedMessageProps,
} from "./types";
import { Workspace } from "../interfaces";

export const decryptionHandler = async (
  email: string,
  token: string,
  dispatch: AppDispatch
): Promise<ChatMessageProps[][] | null> => {
  try {
    const encryptedData: EncryptedDataProps[] = await getAllData(Stores.Users);
    if (!encryptedData || encryptedData.length === 0) {
      // console.log("No chat history found");
      return null;
    }

    const hashValues = await getHashValues(encryptedData);
    if (hashValues.length === 0) return null;
    
    const chatMessages = await getAllChatMessages(hashValues, email, token);
    if (chatMessages.length === 0) return null;
    
    const messages = await getApiResponse(chatMessages);
    if (messages.length === 0) return null;
    
    let workspacesList: Workspace[] = [];
    try {
      workspacesList = await fetchWorkspaces(token);
    } catch (error) {
      console.error("Error fetching workspaces:", error);
      // Continue without workspace validation if fetch fails
    }
    
    let oneTimeCodeExecute = true;
    if (oneTimeCodeExecute) {
      setCompleteMessageOnLoad(messages, dispatch);
      oneTimeCodeExecute = false;
    }

    const workspaceIds = new Set(
      workspacesList.map((workspace) => workspace.id)
    );
    
    // Only validate workspaces if we have workspace data
    const validEncryptedData = workspacesList.length > 0 
      ? encryptedData.filter((data) => {
          if (data.workspaceId && !workspaceIds.has(data.workspaceId)) {
            return false;
          }
          return hashValues.includes(data.hash);
        })
      : encryptedData.filter((data) => hashValues.includes(data.hash));

    if (validEncryptedData.length !== encryptedData.length) {
      // Identify and delete rows that are not in validEncryptedData
      const validIds = new Set(validEncryptedData.map((data) => data.id));
      const invalidData = encryptedData.filter(
        (data) => !validIds.has(data.id)
      );
      
      try {
        for (const data of invalidData) {
          await deleteData(Stores.Users, data.id);
        }
      } catch (error) {
        console.error("Error deleting invalid data:", error);
        // Continue even if deletion fails
      }
    }

    dispatch(clearRecentChat());
    if (validEncryptedData && messages) {
    await recentChatTabsData(validEncryptedData, messages, dispatch);
    }
    

    return messages;
  } catch (error) {
    console.error("Error during decryption:", error);
    return []; // Return an empty array in case of error
  }
};

async function getHashValues(
  encryptedData: EncryptedDataProps[]
): Promise<string[]> {
  if (!encryptedData || encryptedData.length === 0) {
    // console.log("No encrypted data found");
    return []; // Return empty array instead of throwing an error
  }
  return encryptedData.map((data) => data.hash);
}

async function getAllChatMessages(
  hashValues: string[],
  email: string,
  token: string
): Promise<TransformedMessageProps[][]> {
  if (hashValues.length === 0) return [];
  
  const temp: TransformedMessageProps[][] = [];
  const validHashValues: string[] = [];
  
  for (const hash of hashValues) {
    try {
      const newChat = await chatDecryption(hash, email, token);
      if (newChat) {
        // Only push if decryption was successful
        temp.push(newChat as TransformedMessageProps[]);
        validHashValues.push(hash); // Keep track of valid hashes
      }
    } catch (error) {
      console.error(`Error decrypting chat with hash ${hash}:`, error);
      // Continue with next hash
    }
  }
  
  // Update hashValues to only include valid hashes
  hashValues.length = 0;
  hashValues.push(...validHashValues);
  
  return temp;
}

async function getApiResponse(
  chatMessages: TransformedMessageProps[][]
): Promise<ChatMessageProps[][]> {
  if (chatMessages.length === 0) return [];
  
  const temp: ChatMessageProps[][] = [];
  
  for (const messages of chatMessages) {
    if (Array.isArray(messages)) {
      try {
        const transformedMessages = apiResponseMessagesTransform(messages);
        temp.push(transformedMessages);
      } catch (error) {
        console.error("Error transforming messages:", error);
        // Continue with next message set
      }
    } else {
      console.error("Expected an array of messages, but got:", messages);
    }
  }
  
  return temp;
}

function setCompleteMessageOnLoad(
  messages: ChatMessageProps[][],
  dispatch: AppDispatch
) {
  if (!messages || messages.length === 0) return;
  
  let messageLength = 0;
  const obj: CompleteMessageState = {};
  
  for (const message of messages) {
    if (message.length > messageLength) {
      messageLength = message.length;
    }
  }
  
  for (let i = 0; i < messageLength; i++) {
    if (i % 2 !== 0) {
      obj[i] = true;
    }
  }
  
  dispatch(setCompleteMessage(obj));
}