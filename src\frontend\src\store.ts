import PdfViewerReducer from './features/pdfViewerSlice';
import apiCallFlagReducer from './features/apiCallFlagSlice';
import chatHistoryOpenReducer from './features/chatHistoryOpenSlice';
import chatReducer from './features/chatSlice';
import completeMessageReducer from './features/completeMessageSlice';
import currentChatIdReducer  from './features/currentChatIdSlice';
import currentChatLabelReducer from './features/currentChatLabelSlice';
import currentWorkspaceIdReducer from './features/currentWorkspaceIdSlice';
import emailReducer from './features/emailSlice';
import recentChatReducer from './features/recentChatSlice';
import tokenReducer from './features/tokenSlice';
import userReducer from "./features/userSlice";
import workspaceReducer from './features/workspaceSlice';
import wrongFileUploadReducer from './features/wrongFileUploadSlice';
import settingsReducer from './features/settingsSlice';
import versionReducer from "./features/versionSlice";
import regionReducer from "./features/regionSlice";
import translationFilesUploadReducer from './features/translationFilesUploadFlagSlice';
import translationLanguageSelectionReducer from './features/translationLanguageSlice';
import allChatHistoryReducer from './features/AllChatHistorySlice';
import customTemplateReducer from './features/customTemplatesSlice';
import { configureStore } from '@reduxjs/toolkit';
import followUpQuestionsReducer from './features/followUpQuestionsSlice';
import sessionFlagReducer from './features/sessionFlagSlice';

const store = configureStore({
  reducer: {
    pdfViewer: PdfViewerReducer,
    apiCallFlag: apiCallFlagReducer,
    chat: chatReducer,
    chatHistoryOpen: chatHistoryOpenReducer,
    completeMessage : completeMessageReducer,
    currentChatId: currentChatIdReducer,
    currentChatLabel: currentChatLabelReducer,
    currentWorkspaceId : currentWorkspaceIdReducer,
    email: emailReducer,
    recentChat: recentChatReducer,
    token: tokenReducer,
    region: regionReducer,
    user: userReducer,
    workspace: workspaceReducer,
    wrongFileUpload: wrongFileUploadReducer,
    settings: settingsReducer,
    version: versionReducer,
    followUpQuestions: followUpQuestionsReducer,
    translationFilesUpload: translationFilesUploadReducer,
    translationLanguageSelection: translationLanguageSelectionReducer,
    allChat: allChatHistoryReducer,
    customTemplate: customTemplateReducer,
    sessionFlag: sessionFlagReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;