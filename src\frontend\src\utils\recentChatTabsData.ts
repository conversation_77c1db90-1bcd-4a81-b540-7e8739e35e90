import { ChatMessageProps, ChatTabsProps } from "./types";
import { clearRecentChat, setRecentChat } from "../features/recentChatSlice";
import { AppDispatch } from "../store";
import { CHAT_MSSG_ROLE_USER, CHAT_TAB_TEXT_LENGTH, MAX_CHAT_WINDOW_LENGTH } from "../constants/constants";
import { AllChatState } from "../interfaces";
import { getAllData } from "../db/chatDB";
import { Stores } from "../constants/dbConstants";

export const recentChatTabsData = async (allChatHistory: AllChatState, dispatch: AppDispatch): Promise<ChatTabsProps[]> => {

    if(!allChatHistory.allChat){
        return [];
    }

    // Get all data from the database
    const dbHistory = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

    // If there is no data in the database, clear the history and return
    if (dbHistory.length === 0) {
        dispatch(clearRecentChat());
        return [];
    }

    // 🔥 ENHANCEMENT: Handle both complete and partial chat history updates
    // Get the IDs from the database (authoritative source) rather than just from allChatHistory
    let ids = dbHistory.map(record => record.id);

    // For each database ID, ensure we have the chat data
    // If not in allChatHistory, we'll need to skip it for now (it will be loaded by decryptionHandler)
    ids = ids.filter(id => allChatHistory.allChat[id] || dbHistory.some(record => record.id === id));

    // Create a map of ID to date for sorting by date
    const idDateMap = dbHistory.reduce((map, record: { id: string; date: string }) => {
        if (record.date) {
            map[record.id] = new Date(record.date).getTime();
        }
        return map;
    }, {} as Record<string, number>);

    // Sort the IDs by date in descending order (most recent first)
    ids = ids.sort((a, b) => {
        // If both IDs have dates, sort by date
        if (idDateMap[a] && idDateMap[b]) {
            return idDateMap[b] - idDateMap[a];
        }
        // If only one has a date, put the one with a date first
        if (idDateMap[a]) return -1;
        if (idDateMap[b]) return 1;
        // If neither has a date, maintain the original order
        return 0;
    });

    // Limit to MAX_CHAT_WINDOW_LENGTH (10) chats
    if (ids.length > MAX_CHAT_WINDOW_LENGTH) {
        ids = ids.slice(0, MAX_CHAT_WINDOW_LENGTH);
    }

    // 🔥 ENHANCEMENT: Map the IDs to the chat data, handling missing data gracefully
    let chatData: (ChatMessageProps[] | undefined)[] = ids.map(id => allChatHistory.allChat[id]);

    // Filter out undefined chat data and their corresponding IDs
    const validChatData: ChatMessageProps[][] = [];
    const validIds: string[] = [];

    for (let i = 0; i < ids.length; i++) {
        if (chatData[i] && chatData[i]!.length > 0) {
            validChatData.push(chatData[i]!);
            validIds.push(ids[i]);
        }
    }

    // If there is no valid chat data, clear the history and return
    if (validIds.length === 0) {
        dispatch(clearRecentChat());
        return [];
    }

    // Clear the current history before adding the new chats
    dispatch(clearRecentChat());
    const transformedChatData = transformChatData(validIds, validChatData);
    transformedChatData.forEach((chat) => {
        dispatch(setRecentChat(chat));
    });


    return transformedChatData;
}

const transformChatData = (ids: string[], chatData: ChatMessageProps[][]): ChatTabsProps[] => {
    const temp: ChatTabsProps[] = [];

    for (let i = 0; i < chatData.length; i++) {
        if (chatData[i] && chatData[i].length > 0) {
            // Prioritize chatLabel from relabel functionality, fallback to text
            let text = chatData[i][0].user === CHAT_MSSG_ROLE_USER ?
                chatData[i][0].chatLabel || chatData[i][0].text :
                chatData[i][0].text;

            const imgTagIndex = text.indexOf("<img");
            if (imgTagIndex !== -1) {
                text = text.substring(0, imgTagIndex).trim();
            }
            if (text.length > CHAT_TAB_TEXT_LENGTH) {
                text = text.substring(0, CHAT_TAB_TEXT_LENGTH) + "...";
            }
            temp.push({ id: ids[i], text });
        }
    }
    // 🔥 FIX: Don't reverse since we already sorted by date descending (newest first)
    // The IDs are already sorted with newest at the top, so we maintain that order
    return temp;
}
