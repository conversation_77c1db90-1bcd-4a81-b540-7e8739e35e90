import { useEffect, useState } from "react";
import { fetchWorkspaces } from "../services/workspacesService";
import { Workspace } from "../interfaces";
import { useAuth } from "../hooks/useAuth";

const useFetchWorkspaces = () => {
  const { acquireToken, activeAccount } = useAuth();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getWorkspaces = async () => {
      try {
        if (!activeAccount && !acquireToken) return;

        const getToken = await acquireToken();

        if (!getToken) {
          throw new Error("Token acquisition failed.");
        }

        const workspaceData = await fetchWorkspaces(getToken.accessToken);
        setWorkspaces(workspaceData);
      } catch (error) {
        console.error("Error fetching workspaces:", error);
        setError("Failed to load workspaces.");
      } finally {
        setLoading(false);
      }
    };

    getWorkspaces();
  }, [acquireToken, activeAccount]);

  return { workspaces, loading, error, setWorkspaces };
};

export default useFetchWorkspaces;