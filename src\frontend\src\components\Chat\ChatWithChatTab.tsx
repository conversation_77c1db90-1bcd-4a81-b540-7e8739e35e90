import Chat from "./Chat";
import ChatTab from "../TabsSection/ChatTab/ChatTab";
import React, { useEffect } from "react";
import { ChatHistory24Filled } from "@fluentui/react-icons";
import { MAX_RESOLUTION_FOR_MOBILE } from "../../constants/HeaderConstants";
import { RootState } from "../../store";
import { setChatHistoryOpen } from "../../features/chatHistoryOpenSlice";
import { useSelector, useDispatch } from "react-redux";

const ChatWithChatTab: React.FC = () => {
  const label = useSelector((state: RootState) => state.currentChatLabel.label);
  const isOpen = useSelector(
    (state: RootState) => state.chatHistoryOpen.isOpen
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (window.innerWidth < MAX_RESOLUTION_FOR_MOBILE) {
      dispatch(setChatHistoryOpen(false));
    }
  }, [dispatch]);

  const handleChatHistoryToggle = () => {
    dispatch(setChatHistoryOpen(!isOpen));
  };

  return (
    <div className="flex flex-col lg:flex-row h-full w-full">
      <div className="hidden lg:block">
        <ChatTab />
      </div>
      <div className="block lg:hidden w-full">
        <div className="flex justify-between items-center px-4 py-2 bg-wild-sand-50 dark:bg-zinc-800">
          <div className="flex-1 truncate">
            <span className="text-gallagher-blue-400 dark:text-gallagher-blue-200">
              Chat
            </span>{" "}
            {label && <span className="text-black dark:text-white">{">"}</span>}{" "}
            <span className="truncate text-gallagher-blue-400 dark:text-gallagher-blue-200">
              {label}
            </span>
          </div>
          <div className="relative">
            <button
              className="ml-4 p-2 bg-gallagher-blue-400 text-white rounded-sm fixed top-[64px] right-4 z-10"
              onClick={handleChatHistoryToggle}
            >
              <ChatHistory24Filled className="mr-2" />
              Chat History
            </button>
          </div>
        </div>
        <div
          className={`fixed inset-0 top-[115px] bg-white dark:bg-zinc-800 border rounded shadow-lg ${
            isOpen ? "block z-10" : "hidden"
          }`}
        >
          <ChatTab />
        </div>
      </div>
      <Chat />
    </div>
  );
};

export default React.memo(ChatWithChatTab);
