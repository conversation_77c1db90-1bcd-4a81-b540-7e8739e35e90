import { Checkmark24Filled, Dismiss24Filled } from "@fluentui/react-icons";
import React, { useState } from "react";
import { EditableChatTabLabelProps } from "../../interfaces";

const EditableChatTabLabel: React.FC<EditableChatTabLabelProps> = ({
  text,
  onSave,
  onCancel,
}) => {
  const [editedText, setEditedText] = useState(text);
  const handleSave = () => {
    const trimmed = editedText.trim();
    if (trimmed && trimmed !== text) {
      onSave(trimmed);
    } else {
      onCancel();
    }
  };
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      onCancel();
    }
  };

  return (
    <div className="flex items-center w-full gap-1">
      <input
        value={editedText}
        onChange={(e) => setEditedText(e.target.value)}
        onKeyDown={handleKeyDown}
        className="flex-1 px-2 py-1 text-sm border rounded"
      />
      <button
        onClick={handleSave}
        className="text-green-500 hover:text-green-800 cursor-pointer"
      >
        <Checkmark24Filled />
      </button>
      <button
        onClick={onCancel}
        className="text-red-500 hover:text-red-800 cursor-pointer"
      >
        <Dismiss24Filled />
      </button>
    </div>
  );
};

export default EditableChatTabLabel;
