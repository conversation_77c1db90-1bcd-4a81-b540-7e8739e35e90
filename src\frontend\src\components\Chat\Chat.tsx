import React, { useEffect, useRef, useState, useCallback } from "react";
import ChatInput, { ChatInputRef } from "./ChatInput";
import ChatMessage from "./ChatMessage";
import PdfViewer from "../PdfViewer/PdfViewer";
import FollowUpQuestions from "./FollowUpQuestions";
import { RootState } from "../../store";
import store from "../../store";
import { useAppSelector, useAppDispatch } from "../../app/hooks";
import { initDB } from "../../db/chatDB";
import { getUserSetting, initUserSettingDB, saveSelectedCustomTemplate, saveUserSetting } from "../../db/settingDB";
import { encryptionHandler } from "../../utils/encryptionHandler";
import { decryptionHandler } from "../../utils/decryptionHandler";
import { closePanel } from "../../features/pdfViewerSlice";
import { USER_SETTINGS_ID } from "../../constants/dbConstants";
import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY, SYSTEM_TEMPLATE } from "../../constants/SettingsTabConstants";
import { setSetting } from "../../features/settingsSlice";
import { createSelector } from '@reduxjs/toolkit';
import { ChatMessageProps } from "../../utils/types";
import { setSessionCallFlag } from "../../features/sessionFlagSlice";

const Chat: React.FC = () => {
// Memoized selectors for better performance
const selectMessages = (state: RootState) => state.chat.messages;
const selectEmail = (state: RootState) => state.email.email;
const selectToken = (state: RootState) => state.token.token;
const selectCurrentChatId = (state: RootState) => state.currentChatId.id;
const selectCurrentWorkspaceId = (state: RootState) => state.currentWorkspaceId.id;
const selectSelectedPdf = (state: RootState) => state.pdfViewer.pdfUrl;
const selectFollowUpQuestions = (state: RootState) => state.followUpQuestions.questions;

// Create a memoized selector for chat data to prevent unnecessary re-renders
const selectChatData = createSelector(
  [
    selectMessages,
    selectEmail,
    selectToken,
    selectCurrentChatId,
    selectCurrentWorkspaceId,
    selectSelectedPdf,
    selectFollowUpQuestions
  ],
  (messages, email, token, currentChatId, currentWorkspaceId, selectedPdf, followUpQuestions) => ({
    messages,
    email,
    token,
    currentChatId,
    currentWorkspaceId,
    selectedPdf,
    followUpQuestions
  })
);
// Use typed selector hook for better type safety
const chatData = useAppSelector(selectChatData);
const { messages, email, token, currentChatId, currentWorkspaceId, selectedPdf, followUpQuestions } = chatData;
  const messagesEndRef = useRef<null | HTMLDivElement>(null);
  const lastCopyButtonRef = useRef<null | HTMLDivElement>(null);
  const [completeMessages, setCompleteMessages] = useState<{
    [key: number]: boolean;
  }>({});
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  const chatInputRef = useRef<ChatInputRef>(null);
  const [showFollowUp, setShowFollowUp] = useState(false);
  const newSessionFlag = useAppSelector((state: RootState) => state.sessionFlag.status);
  const scrollToBottom = useCallback(() => {
    if (lastCopyButtonRef.current) {
      lastCopyButtonRef.current.scrollIntoView({ behavior: "smooth" });
      setTimeout(() => {
        lastCopyButtonRef.current?.scrollIntoView({ behavior: "auto" });
      }, 100);
    } else {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });

      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "auto" });
      }, 100);
    }
  }, []);
  const setLastCopyButtonRef = useCallback((element: HTMLDivElement | null) => {
    lastCopyButtonRef.current = element;
  }, []);

  useEffect(()=>{
    if(newSessionFlag){
      async function init() {
        await initDB();
        await initUserSettingDB();
        await saveSelectedCustomTemplate(SYSTEM_TEMPLATE[0]);
        const defaultSettings = {
          id: USER_SETTINGS_ID,
          temperature: TEMPERATURE.defaultValue,
          topP: TOP_P.defaultValue,
          frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
          presencePenalty: PRESENCE_PENALTY.defaultValue,
          template: "",
        };
        const settings = await getUserSetting(USER_SETTINGS_ID);
        if (!settings) {
          await saveUserSetting(defaultSettings);
          dispatch(setSetting(defaultSettings));
        }
        else {
          settings.template = "";
          await saveUserSetting(settings);
        }
        dispatch(setSessionCallFlag(false));
        dispatch(setSetting(settings));
      }
      init();
    }
  },[])

  useEffect(() => {
    if (token && email && messages.length === 0) {
      const fetch = async () => {
        try {
          await decryptionHandler(email, token, dispatch);
          // The decryptionHandler now populates the Redux store and recent chat tabs
          // No need to manually set messages here as they're handled by Redux
        } catch (error) {
          console.error("Error in decryptionHandler:", error);
        }
      };
      fetch();
    }
  }, [token, email, messages.length, dispatch]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  useEffect(() => {
    if (messages.length > 0 && completeMessages[messages.length - 1]) {
      encryptionHandler(
        messages,
        email,
        token,
        dispatch,
        currentChatId,
        currentWorkspaceId,
        store.getState
      );
    }
  }, [completeMessages]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     await initDB();
  //     await initUserSettingDB();
  //     const data = await getUserSetting(USER_SETTINGS_ID);
  //     const selectedTemplate = await getSelectedCustomTemplate();
  //     let template ="";
  //     if(selectedTemplate) {
  //       template =selectedTemplate.description===SYSTEM_TEMPLATE[0].description ? "" : selectedTemplate.description;
  //     }
  //     if (!data) {
  //       const defaultSettings = {
  //         id: USER_SETTINGS_ID,
  //         temperature: TEMPERATURE.defaultValue,
  //         topP: TOP_P.defaultValue,
  //         frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
  //         presencePenalty: PRESENCE_PENALTY.defaultValue,
  //         template: "",
  //       };
  //       saveUserSetting(defaultSettings);
  //       dispatch(setSetting(defaultSettings));
  //     } else {
  //       dispatch(setSetting(data));
  //       saveUserSetting(data);
  //     }
  //   };
  //   fetchData();
  // }, []);

  // Animation effect for follow-up questions
  useEffect(() => {
    if (followUpQuestions.length > 0) {
      setShowFollowUp(false);
      setTimeout(() => {
        setShowFollowUp(true);
        setTimeout(() => {
          scrollToBottom();
        }, 350);
      }, 50);
    } else {
      setShowFollowUp(false);
    }
  }, [followUpQuestions, scrollToBottom]);

  // Function to mark a message as complete
  const markMessageComplete = (index: number) => {
    setCompleteMessages((prev) => ({ ...prev, [index]: true }));
  };

  const handleClosePanel = () => {
    dispatch(closePanel());
  };

  const handleSelectQuestion = useCallback((question: string) => {
    if (chatInputRef.current) {
      chatInputRef.current.setInputValue(question);
      setTimeout(() => {
        const textarea = document.querySelector('textarea[aria-label="Chat input"]');
        if (textarea) {
          const inputElement = textarea as HTMLTextAreaElement;
          inputElement.focus();
          inputElement.setSelectionRange(question.length, question.length);
        }
      }, 100);
    }
  }, []);

  return (
    <div className="flex h-full w-full">
      <div className={`flex flex-col ${selectedPdf ? "w-1/2 mobileViewChat" : "w-full"}`}>
        <div className="flex-1 bg-wild-sand-50 dark:bg-zinc-800">
          {/* Use useMemo to prevent recreating the array of ChatMessage components on every render */}
          {React.useMemo(() =>
            messages.map((msg: ChatMessageProps, index: number) => (
              <ChatMessage
                key={index}
                user={msg.user}
                text={msg.text}
                messageId={index}
                isStreamComplete={!!completeMessages[index]}
                workspaceId={currentWorkspaceId}
                documentId={msg.documentId || ""}
                copyButtonRef={index === messages.length - 1 ? setLastCopyButtonRef : undefined}
                chatLabel={""}
              />
            )),
            [messages, completeMessages, currentWorkspaceId, setLastCopyButtonRef]
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="sticky bottom-0 bg-wild-sand-50 dark:bg-zinc-800">
          {/* Renders FollowUpQuestions with animation */}
          {messages.length > 0 && followUpQuestions.length > 0 && (
            <div
              className={`transition-all duration-300 ease-in-out ${
                showFollowUp
                  ? 'opacity-100 transform translate-y-0 max-h-[300px]'
                  : 'opacity-0 transform translate-y-4 max-h-0 overflow-hidden'
              }`}
            >
              <FollowUpQuestions onSelectQuestion={handleSelectQuestion} />
            </div>
          )}

          {/* Renders ChatInput */}
          <ChatInput ref={chatInputRef} onMessageComplete={markMessageComplete} />
        </div>
      </div>

      {selectedPdf && (
        <div className="w-1/2 mobileViewPdfPanel">
          <PdfViewer pdfUrl={selectedPdf} onClosePanel={handleClosePanel} />
        </div>
      )}
    </div>
  );
};

export default Chat;
