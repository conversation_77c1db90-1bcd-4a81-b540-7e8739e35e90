import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "../store";
import { checkVersionChange, markUpdatesAsViewed } from "../features/versionSlice";

const useVersionCheck = () => {
  const dispatch = useDispatch<AppDispatch>();
  const isUpdateAvailable = useSelector((state: RootState) => state.version.isUpdateAvailable);

  useEffect(() => {
    dispatch(checkVersionChange());
  }, [dispatch]);

  return { isUpdateAvailable, markUpdatesAsViewed: () => dispatch(markUpdatesAsViewed()) };
};

export default useVersionCheck;
