import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { EmailState } from "../interfaces";

const initialState: EmailState = {
  email: "",
};

const emailSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setEmail: (state, action: PayloadAction<string>) => {
      state.email = action.payload;
    },
  },
});

export const { setEmail } = emailSlice.actions;

export default emailSlice.reducer;
