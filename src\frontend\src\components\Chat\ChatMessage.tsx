import React, { useCallback } from "react";
import { Person24Filled, Copy20Regular } from "@fluentui/react-icons";
import ReactMarkdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import rehypeSanitize from "rehype-sanitize";
import "github-markdown-css/github-markdown.css";
import useThemeSwitcher from "../../hooks/useThemeSwitcher";
import useCodeBlocksThemeSwitcher from "../../hooks/useCodeBlocksThemeSwitcher";
import DotsAnimation from "../LoadAnimation/DotsAnimation";
import aiRobot from "../../assets/aiRobot.png";
import { ChatMessageProps } from "../../utils/types";
import useChatMessage from "../../hooks/useChatMessage";

// Memo comparison function for better performance
const arePropsEqual = (prevProps: ChatMessageProps, nextProps: ChatMessageProps) => {
  return (
    prevProps.user === nextProps.user &&
    prevProps.text === nextProps.text &&
    prevProps.messageId === nextProps.messageId &&
    prevProps.isStreamComplete === nextProps.isStreamComplete &&
    // Only check reference equality for the callback function
    prevProps.copyButtonRef === nextProps.copyButtonRef
  );
};

const ChatMessage: React.FC<ChatMessageProps> = React.memo(({ 
  user, 
  text, 
  messageId,
  isStreamComplete, 
  copyButtonRef 
}) => {
  const {
    isHovered,
    setIsHovered,
    copied,
    apiCall,
    isBot,
    messageClass,
    isError,
    errorDetails,
    imgTags,
    cleanText,
    citationsArray,
    handleCopyClick,
    handleCitationClick,
    messageId: messageLength,
    highlightedCitation
  } = useChatMessage(user, text, messageId);

  // Only run these hooks if the component is actually mounted
  useCodeBlocksThemeSwitcher();
  useThemeSwitcher();

  // Memoize local handlers
  const onMouseEnter = useCallback(() => setIsHovered(true), [setIsHovered]);
  const onMouseLeave = useCallback(() => setIsHovered(false), [setIsHovered]);

  return (
    <div className={`flex p-1 space-y-2 ${isBot ? "justify-start" : "justify-end"}`}>
      {isBot && (
        <img
          src={aiRobot}
          alt="AI Assistant"
          className="shrink-0 w-auto h-10 bg-white dark:bg-black rounded-full mr-2"
        />
      )}
      <div className={`relative p-4 mt-2 rounded-2xl max-w-full overflow-hidden ${messageClass}`}>
        {text ? (
          isError ? (
            <div className="text-red-700 dark:text-red-400">{errorDetails}</div>
          ) : (
            <div className={`${isBot ? "bot-message" : "user-message"}`}>
              <ReactMarkdown
                rehypePlugins={
                  isBot
                    ? [rehypeHighlight, rehypeRaw]
                    : [rehypeHighlight, rehypeRaw, rehypeSanitize]
                }
                remarkPlugins={[remarkGfm]}
              >
                {cleanText}
              </ReactMarkdown>
              {imgTags.length > 0 && (
                <div className="flex col">
                  {imgTags.map((src, index) => (
                    <img
                      src={src}
                      alt="Image Preview"
                      key={index}
                      className="max-w-[250px] max-h-[250px] mb-2"
                    />
                  ))}
                </div>
              )}
            </div>
          )
        ) : (
          <DotsAnimation />
        )}
        {citationsArray.length > 0 && (
          <div className="space-y-2 mt-4">
            <p className="text-sm font-medium text-black dark:text-white">Citations:</p>
            {citationsArray.map((citation, index) => {
              const citationId = `citation-${messageLength}${index}`;
              const isHighlighted = highlightedCitation === citationId;
              
              return (
                <button
                  key={index}
                  id={citationId}
                  className={`text-xs font-bold cursor-pointer text-gallagher-blue-400 dark:text-gallagher-blue-300 underline rounded 
                    ${isHighlighted 
                      ? 'bg-blue-300 dark:bg-blue-800 citation-highlight' 
                      : 'bg-gray-200 dark:bg-gray-700'} 
                    px-2 py-1 ml-1`}
                  onClick={() => handleCitationClick(citation.link)}
                >
                  {citation.label}
                </button>
              );
            })}
          </div>
        )}
        {isStreamComplete && !apiCall && (
          <div
            ref={copyButtonRef}
            className="bottom-2 h-4 mt-1 right-2 flex items-center cursor-pointer w-full justify-end"
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onClick={handleCopyClick}
          >
            {isHovered && (
              <button className="mr-1 text-xs text-white bg-wild-sand-600 dark:bg-gray-700 p-2 rounded-sm">
                {copied ? "Copied" : "Copy"}
              </button>
            )}
            <Copy20Regular
              className={`h-6 ${isHovered ? "text-black dark:text-white" : "text-wild-sand-600 dark:text-gray-400"}`}
            />
          </div>
        )}
      </div>
      {!isBot && (
        <Person24Filled className="shrink-0 w-auto h-10 bg-link-water-200 dark:bg-gallagher-blue-800 text-waikawa-gray-500 dark:text-gray-400 rounded-full ml-2" />
      )}
    </div>
  );
}, arePropsEqual);

export default ChatMessage;