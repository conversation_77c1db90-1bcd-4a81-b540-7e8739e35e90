import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CurrentChatIdState } from "../interfaces";

const initialState: CurrentChatIdState = {
  id: "",
};

const currentChatIdSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setCurrentChatId: (state, action: PayloadAction<string>) => {
      state.id = action.payload;
    },
  },
});

export const { setCurrentChatId } = currentChatIdSlice.actions;

export default currentChatIdSlice.reducer;
