import React, { useState } from "react";
import { Sparkle24Filled, Add20Filled, Subtract20Regular } from "@fluentui/react-icons";
import { useSelector } from "react-redux";
import { RootState } from "../../store";

interface FollowUpQuestionsProps {
  onSelectQuestion: (question: string) => void;
}

const FollowUpQuestions: React.FC<FollowUpQuestionsProps> = ({ onSelectQuestion }) => {
  const questions = useSelector((state: RootState) => state.followUpQuestions.questions);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  if (!questions || questions.length === 0) {
    return null;
  }

  // Handle question selection
  const handleQuestionClick = (question: string): void => {
    onSelectQuestion(question);
    
    // Auto-collapse after selection on mobile
    setIsExpanded(false);
    
    // Focus on the input field
    setTimeout(() => {
      const textarea = document.querySelector('textarea[aria-label="Chat input"]');
      if (textarea) {
        const inputElement = textarea as HTMLTextAreaElement;
        inputElement?.focus();
      }
    }, 50);
  };

  return (
    <div className="px-4">
      {/* Mobile view - Collapsible header (only visible on small screens) */}
      <button
        onClick={() => setIsExpanded(prev => !prev)}
        className="hidden sm:flex w-full items-center justify-between p-1 bg-wild-sand-50 dark:bg-zinc-800 border border-x-wild-sand-50 border-b-wild-sand-50 dark:border-t-gray-200 dark:border-x-zinc-800 dark:border-b-zinc-800 dark:border-t-gray-200 text-black dark:text-white"
        aria-expanded={isExpanded}
        aria-controls="follow-up-suggestions"
      >
        <div className="flex items-center">
          <Sparkle24Filled className="mr-2 text-indigo-500 dark:text-indigo-400" />
          <span className="font-medium">Suggestions</span>
        </div>
        {isExpanded ? (
          <Subtract20Regular className="text-indigo-500 dark:text-indigo-400" />
        ) : (
          <Add20Filled className="text-indigo-500 dark:text-indigo-400" />
        )}
      </button>

      {/* Desktop view (always visible) & Mobile view (conditionally visible) */}
      <div 
        id="follow-up-suggestions"
        className={`flex flex-wrap gap-2 py-2 ${isExpanded ? 'sm:gap-y-1' : 'sm:hidden'}`}
      >
        {questions.map((question, index) => (
          <button
            key={index}
            onClick={() => handleQuestionClick(question)}
            className="px-3 py-1 cursor-pointer bg-blue-50 dark:bg-gray-700 hover:bg-gallagher-blue-100 dark:hover:bg-gray-800 text-black dark:text-white rounded-xl text-sm flex items-center transition-colors border border-blue-500"
          >
            <Sparkle24Filled className="mr-2 text-indigo-500 dark:text-indigo-400" />
            {question}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FollowUpQuestions;