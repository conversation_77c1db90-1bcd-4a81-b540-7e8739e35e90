import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../store";
import { openPanel, closePanel } from "../features/pdfViewerSlice";
import { setFollowUpQuestions } from '../features/followUpQuestionsSlice';
import { parsePdfUrl } from "../utils/pdfUtils";
import { extractFromMessageUtils } from "../utils/extractFromMessageUtils";
import { CHAT_MSSG_ROLE_ASSISTANT } from "../constants/constants";

const useChatMessage = (user: string, text: string, index: number) => {
  const [isHovered, setIsHovered] = useState(false);
  const [copied, setCopied] = useState(false);
  const [highlightedCitation, setHighlightedCitation] = useState<string | null>(null);
  const citationLinksRef = useRef<NodeListOf<Element> | null>(null);
  const selectedPdf = useSelector((state: RootState) => state.pdfViewer.pdfUrl);
  const dispatch = useDispatch();

  const apiCall = useSelector((state: RootState) => state.apiCallFlag.status);
  const messages = useSelector((state: RootState) => state.chat.messages);
  const currentQuestions = useSelector((state: RootState) => state.followUpQuestions.questions);
  const messageId = index;
  const isBot = user === CHAT_MSSG_ROLE_ASSISTANT;

  // Memoize the expensive parsing operation
  const parsedContent = useMemo(() => {
    return isBot 
      ? extractFromMessageUtils.parseContent(text, messageId) 
      : { parsedText: text, citations: [], followUpQuestions: [], pdfName: [] };
  }, [isBot, text]);
  
  const { parsedText, citations, followUpQuestions, pdfName } = parsedContent;

  const messageClass = isBot
    ? "markdown-body bg-white rounded-tl-none"
    : "bg-link-water-200 dark:bg-gallagher-blue-800 dark:text-gray-200 rounded-tr-none message-line-wrap";

  // Memoize these checks to avoid recalculating on every render
  const { isError, errorDetails, imgTags, cleanedText } = useMemo(() => {
    const isError = text.trim().startsWith("{") && text.includes("ExceptionType") && text.includes("Message");
    const errorDetails = isError ? extractFromMessageUtils.extractErrorDetails(text) : null;
    const imgTags = extractFromMessageUtils.extractImagesFromText(text);
    const cleanedText = extractFromMessageUtils.cleanTextFromImages(text);
    
    return { isError, errorDetails, imgTags, cleanedText };
  }, [text]);

  const cleanText = parsedText;
  
  // Memoize the citations array to prevent unnecessary recreations
  const citationsArray = useMemo(() => {
    return citations.map((citation, index) => ({
      label: citation,
      link: pdfName[index]
    }));
  }, [citations, pdfName]);

  // Function to highlight a citation temporarily
  const highlightCitation = useCallback((citationId: string) => {
    setHighlightedCitation(citationId);
    
    // Remove highlight after 2 seconds
    setTimeout(() => {
      setHighlightedCitation(null);
    }, 2000);
  }, []);

  useEffect(() => {
    if (isBot && followUpQuestions.length > 0 || currentQuestions) {
      dispatch(setFollowUpQuestions(followUpQuestions));
    }
  }, [followUpQuestions.length, isBot, currentQuestions, dispatch]);

  // Function to highlight the corresponding pdf button when an anchor link is clicked in a message
  const smoothScrollToElement = useCallback((element: HTMLElement) => {
    // Header height (56px see TopComponent.tsx pt class)
    const headerHeight = 56;
    
    // Calculates the position for the element to show it on the top third of the screen
    const elementRect = element.getBoundingClientRect();
    const elementPosition = elementRect.top + window.pageYOffset;
    const offsetPosition = elementPosition - (headerHeight + window.innerHeight / 5);
    
    // Do the scrolling
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    });
  }, []);

  useEffect(() => {
    citationLinksRef.current = document.querySelectorAll(".citation-link");
    citationLinksRef.current.forEach((link) => {
      const handleClick = (event: Event) => {
        event.preventDefault();
        const index = link.getAttribute("data-index");
        const targetElement = document.getElementById(`citation-${index}`);
        if (targetElement) {
          // Highlight the citation when scrolling to it
          highlightCitation(`citation-${index}`);
          
          // Usar nuestro scroll personalizado en lugar de scrollIntoView
          smoothScrollToElement(targetElement);
        } else {
          console.error(`Element with ID citation-${index} not found.`);
        }
      };
      link.addEventListener("click", handleClick);
      return () => link.removeEventListener("click", handleClick);
    });
  }, [cleanText, highlightCitation, smoothScrollToElement]);

  const handleCopyClick = useCallback(async () => {
    try {
      const htmlText = await extractFromMessageUtils.extractHtml(cleanText);
      const plainText = extractFromMessageUtils.extractPlainText(cleanText);
      const clipboardItem = new ClipboardItem({
        "text/html": new Blob([htmlText], { type: "text/html" }),
        "text/plain": new Blob([plainText], { type: "text/plain" }),
      });
      await navigator.clipboard.write([clipboardItem]);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Could not copy text: ", err);
    }
  }, [cleanText]);

  const handleCitationClick = useCallback((link: string) => {
    
    const { fileName, pageParam } = parsePdfUrl(link);
    const newPageNumber = pageParam ? parseInt(pageParam.split("=")[1], 10) : 1;

    if (selectedPdf) {
      const { fileName: currentFileName, pageParam: currentPageParam } = parsePdfUrl(selectedPdf);
      const currentPageNumber = currentPageParam ? parseInt(currentPageParam.split("=")[1], 10) : 1;

      if (fileName === currentFileName && newPageNumber === currentPageNumber) {
        dispatch(closePanel());
        return;
      }
    }

    dispatch(openPanel(link));
  }, [selectedPdf, dispatch, highlightCitation]);

  return {
    isHovered,
    setIsHovered,
    copied,
    setCopied,
    highlightedCitation,
    citationLinksRef,
    selectedPdf,
    apiCall,
    messages,
    messageId,
    isBot,
    messageClass,
    isError,
    errorDetails,
    imgTags,
    cleanedText,
    cleanText,
    citationsArray,
    followUpQuestions,
    pdfName,
    handleCopyClick,
    handleCitationClick,
  };
};

export default useChatMessage;